{"expo": {"name": "Manito", "slug": "manito", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"buildNumber": "*********", "supportsTablet": true, "bundleIdentifier": "co.manito.app", "usesAppleSignIn": true, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to let you choose pictures.", "UIBackgroundModes": ["remote-notification"]}, "appleTeamId": "8Z69NK39Q7"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "co.manito.app"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-apple-authentication"], "expo-secure-store", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.684414254538-iea4399buqu70spud45kkq457i5mpckv"}], "expo-notifications", "expo-localization", "expo-audio"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "0811782e-882c-4ab6-bf7a-9f485d3a9e84"}}}}