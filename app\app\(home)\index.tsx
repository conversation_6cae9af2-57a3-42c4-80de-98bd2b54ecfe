import React, { useState, useRef, useEffect } from 'react';
import { signInWithApple } from '@/utils/AppleAuth';
import Page from '@/components/templates/Page';
import BottomSheet from '@/components/templates/BottomSheet';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Platform,
  Animated,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import Header from '@/components/templates/Header';
import ChatInputArea from '@/components/organisms/chat/ChatInputArea';
import { Ingredient } from '@/types/ingredient';
import { ChatMessage } from '@/types/chat';
import { ChatAgentBubbles } from '@/components/organisms/chat/ChatAgentBubbles';
import ChatStarter from '@/components/organisms/chat/ChatStarter';
import { useChat } from '@/context/ChatContext';
import { ScreenWrapper } from '@/components/atoms/blur/BlurDrawer';
import ScreenLoader from '@/components/atoms/loaders/Screen';

const Index = () => {
  const scrollViewRef = useRef<ScrollView>(null);
  const { messages } = useChat();

  // Animation values
  const chatStarterOpacity = useRef(new Animated.Value(1)).current; // Start with ChatStarter visible
  const chatBubblesOpacity = useRef(new Animated.Value(0)).current; // Start with ChatAgentBubbles hidden


  const scrollToBottom = () => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  };



  useEffect(() => {
    // Animate based on messages length
    if (messages.length > 0) {
      // Fade out ChatStarter, fade in ChatAgentBubbles
      Animated.parallel([
        Animated.timing(chatStarterOpacity, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(chatBubblesOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Fade in ChatStarter, fade out ChatAgentBubbles
      Animated.parallel([
        Animated.timing(chatStarterOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(chatBubblesOpacity, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }

    // Scroll to bottom when messages change
    setTimeout(() => scrollToBottom(), 100);
  }, [messages]);

  return (
    <ScreenWrapper>
      <Page noPaddingTop noBottomBar alignItems="center" justifyContent="space-between" page="home">
        <Header burgerMenu buttonWrite text=' ' />


        <ScrollView
          ref={scrollViewRef}
          style={styles.chatContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.chatContent}
        >

          {/* New Chat Fallback (Start Screen) */}
          <Animated.View style={{ opacity: chatStarterOpacity }}>
            {
              messages.length === 0 &&
              <ChatStarter />
            }
          </Animated.View>

          {/* Message Feed */}
          <Animated.View style={{ opacity: chatBubblesOpacity }}>
            {messages.length > 0 &&
              messages.map((m, i) => (
                <ChatAgentBubbles
                  key={i}
                  message={m}
                  userIngredients={[]}
                />
              ))}
          </Animated.View>

        </ScrollView>
        <ChatInputArea />
      </Page>
    </ScreenWrapper>
  );
};

export default Index;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chatContainer: {

    flex: 1,
    width: '100%',
    paddingHorizontal: 15,
    paddingBottom: 30,
  },
  chatContent: {
    width: '100%',
    paddingBottom: 20,
    paddingTop: 100,
  },
});