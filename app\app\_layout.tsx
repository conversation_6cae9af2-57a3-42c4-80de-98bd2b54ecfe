import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider } from '@/context/AuthContext';
import { StatusBarExpo } from '@/components/templates/Statusbar';
import { NotificationsProvider } from '@/context/NotificationsContext';
import { ChatProvider } from '@/context/ChatContext';
import { BottomSheetProvider } from '@/context/BottomSheetContext';
import { useEffect } from 'react';

export default function RootLayout() {
  const colorScheme = useColorScheme();


    // Prevent the splash screen from auto-hiding before asset loading is complete.
    SplashScreen.preventAutoHideAsync();
  
  
    const [loaded] = useFonts({
      //SpaceMono: require('../../assets/fonts/SpaceMono-Regular.ttf'),
    });
  
    useEffect(() => {
      if (loaded) {
        SplashScreen.hideAsync();
      }
    }, [loaded]);
  

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <NotificationsProvider>
          <AuthProvider>
            <BottomSheetProvider>
              <ChatProvider>
                <Stack screenOptions={{ headerShown: false }}>
                  <Stack.Screen name="(home)" options={{ headerShown: false }} />
                  <Stack.Screen name="index" options={{ headerShown: false }} />
                  <Stack.Screen name="+not-found" />
                </Stack>
                <StatusBarExpo style="dark" />
              </ChatProvider>
            </BottomSheetProvider>
          </AuthProvider>
        </NotificationsProvider> 
    </ThemeProvider>
  );
}
