import React from 'react';
import { View, FlatList, Image, Dimensions, TouchableOpacity } from 'react-native';

interface RenderBottomSheetProps {
  photos: { id: string; uri: string }[];
  selectedPhotos?: string[];
  toggleSelectPhoto: (uri: string) => void;
}

const RenderBottomSheet: React.FC<RenderBottomSheetProps> = ({ photos, selectedPhotos, toggleSelectPhoto }) => {
  return (
    <View style={{ padding: 0, flex: 1 }}>
      <FlatList
        data={photos}
        numColumns={4}
        keyExtractor={item => item.id}
        renderItem={({ item }) => {
          //const selected = selectedPhotos.includes(item.uri);
          return (
            <TouchableOpacity
              onPress={() => toggleSelectPhoto(item.uri)}
              style={{
                margin: 2,
                //borderWidth: selected ? 4 : 1,
                //borderColor: selected ? '#337836' : '#ccc',
                borderRadius: 8,
              }}
            >
              <Image
                source={{ uri: item.uri }}
                style={{
                  width: (Dimensions.get('window').width - 55) / 4,
                  height: 110,
                  borderRadius: 5,
                }}
              />
            </TouchableOpacity>
          );
        }}
        extraData={selectedPhotos}
      />
    </View>
  );
};

export default RenderBottomSheet;
