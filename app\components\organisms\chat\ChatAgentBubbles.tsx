import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { ChatMessage } from '@/types/chat'
import { Ionicons } from '@expo/vector-icons'
import { calculateFreshnessPercentage, getFreshnessColor, getFreshnessLabel } from '@/utils/Frenshess'
import { Ingredient } from '@/types/ingredient'

interface ChatAgentBubbles {
    message: ChatMessage,
    userIngredients: Ingredient[]
}

export const ChatAgentBubbles = ({ message, userIngredients }: ChatAgentBubbles) => {
     const [selectedIngredients, setSelectedIngredients] = useState<string[]>([])

    const toggleIngredientSelection = (ingredientId: string) => {
        setSelectedIngredients((prev) => {
            const newSelection = prev.includes(ingredientId)
                ? prev.filter((id) => id !== ingredientId)
                : [...prev, ingredientId]

            // Add bot response about selection
            setTimeout(() => {
                const ingredient = userIngredients.find((ing) => ing.id === ingredientId)
                if (ingredient) {
                    
                }
            }, 300)

            return newSelection
        })
    }



    const renderMessage = (message: ChatMessage) => {
        if (message.type === "ingredient-display") {
            return (
                <View key={message.id} style={styles.messageContainer}>
                    <View style={[styles.messageBubble, styles.botBubble]}>
                        <Text style={styles.botText}>{message.content}</Text>
                        <View style={styles.ingredientsGrid}>
                            {userIngredients.map((ingredient) => {
                                const percentage = calculateFreshnessPercentage(ingredient)
                                const isSelected = selectedIngredients.includes(ingredient.id)
                                return (
                                    <TouchableOpacity
                                        key={ingredient.id}
                                        style={[styles.ingredientCard, isSelected && styles.selectedIngredientCard]}
                                        onPress={() => toggleIngredientSelection(ingredient.id)}
                                    >
                                        <Text style={styles.ingredientEmoji}>{ingredient.emoji}</Text>
                                        <Text style={styles.ingredientName}>{ingredient.name}</Text>
                                        <View style={styles.freshnessContainer}>
                                            <View
                                                style={[
                                                    styles.freshnessBar,
                                                    { backgroundColor: getFreshnessColor(percentage), width: `${percentage}%` },
                                                ]}
                                            />
                                            <Text style={[styles.freshnessText, { color: getFreshnessColor(percentage) }]}>
                                                {percentage}%
                                            </Text>
                                        </View>
                                        <Text style={[styles.freshnessLabel, { color: getFreshnessColor(percentage) }]}>
                                            {getFreshnessLabel(percentage)}
                                        </Text>
                                        {isSelected && (
                                            <View style={styles.selectedIndicator}>
                                                <Ionicons name="checkmark-circle" size={20} color="#337836" />
                                            </View>
                                        )}
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                        {selectedIngredients.length > 0 && (
                            <TouchableOpacity style={styles.findRecipesButton} onPress={() => {}}>
                                <Ionicons name="restaurant" size={16} color="#fff" />
                                <Text style={styles.findRecipesText}>Find Recipes ({selectedIngredients.length})</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                    <Text style={styles.timestamp}>
                        {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </Text>
                </View>
            )
        }

        return (
            <View key={message.id} style={styles.messageContainer}>
                <View style={[styles.messageBubble, message.type === "bot" ? styles.botBubble : styles.userBubble]}>
                    <Text style={message.type === "bot" ? styles.botText : styles.userText}>{message.content}</Text>
                </View>
                <Text style={styles.timestamp}>
                    {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                </Text>
            </View>
        )
    }

    return renderMessage(message)
}


const styles = StyleSheet.create({
    messageContainer: {
        marginBottom: 16,
    },
    messageBubble: {
        maxWidth: "85%",
        padding: 12,
        borderRadius: 16,
        marginBottom: 4,
    },
    botBubble: {
        backgroundColor: "#fff",
        alignSelf: "flex-start",
        borderBottomLeftRadius: 4,
    },
    userBubble: {
        backgroundColor: "#337836",
        alignSelf: "flex-end",
        borderBottomRightRadius: 4,
    },
    botText: {
        fontSize: 16,
        color: "#333",
        lineHeight: 22,
    },
    userText: {
        fontSize: 16,
        color: "#fff",
        lineHeight: 22,
    },
    timestamp: {
        fontSize: 12,
        color: "#999",
        alignSelf: "flex-start",
        marginLeft: 12,
    },
    ingredientsGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 12,
        marginTop: 12,
    },
    ingredientCard: {
        backgroundColor: "#f8f9fa",
        borderRadius: 12,
        padding: 12,
        width: "47%",
        alignItems: "center",
        borderWidth: 2,
        borderColor: "transparent",
        position: "relative",
    },
    selectedIngredientCard: {
        borderColor: "#337836",
        backgroundColor: "#f0f9f0",
    },
    ingredientEmoji: {
        fontSize: 24,
        marginBottom: 4,
    },
    ingredientName: {
        fontSize: 14,
        fontWeight: "600",
        color: "#333",
        marginBottom: 8,
        textAlign: "center",
    },
    freshnessContainer: {
        width: "100%",
        height: 6,
        backgroundColor: "#e9ecef",
        borderRadius: 3,
        marginBottom: 4,
        position: "relative",
    },
    freshnessBar: {
        height: "100%",
        borderRadius: 3,
    },
    freshnessText: {
        position: "absolute",
        right: 0,
        top: -20,
        fontSize: 10,
        fontWeight: "600",
    },
    freshnessLabel: {
        fontSize: 10,
        fontWeight: "500",
        textAlign: "center",
    },
    selectedIndicator: {
        position: "absolute",
        top: 8,
        right: 8,
    },
    findRecipesButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#337836",
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginTop: 16,
        gap: 6,
    },
    findRecipesText: {
        color: "#fff",
        fontSize: 14,
        fontWeight: "600",
    },
    suggestionGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
        marginTop: 12,
    },
    suggestionCard: {
        backgroundColor: "#f8f9fa",
        borderRadius: 8,
        padding: 8,
        alignItems: "center",
        minWidth: 80,
    },
    suggestionEmoji: {
        fontSize: 20,
        marginBottom: 4,
    },
    suggestionName: {
        fontSize: 12,
        color: "#333",
        textAlign: "center",
    },
})