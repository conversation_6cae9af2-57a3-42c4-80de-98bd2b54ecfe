import React, { useContext, useEffect, useState } from 'react'

import { Ionicons } from '@expo/vector-icons'
import { TextInput } from 'react-native-gesture-handler'
import { Alert, Dimensions, FlatList, Image, Keyboard, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import * as MediaLibrary from 'expo-media-library';
import { useChat } from '@/context/ChatContext'
import { useBottomSheet } from '@/context/BottomSheetContext'
import i18n from '@/i18n/i18n'
import RenderBottomSheet from '../bottomsheets/select_picture_bottomsheet';


interface ChatInputAreaProps {

}

const ChatInputArea = ({

}: ChatInputAreaProps) => {
  const { handleSendMessage } = useChat()
  const { handleToggleBottomSheet } = useBottomSheet()

  const [inputText, setInputText] = useState('')
  const [photos, setPhotos] = useState<MediaLibrary.Asset[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);

  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const toggleSelectPhoto = (uri: string) => {
    console.log(uri)
    setSelectedPhoto(uri)
    handleToggleBottomSheet()


    /* if (selectedPhotos.includes(uri)) {
      setSelectedPhotos(selectedPhotos.filter(p => p !== uri));
    } else if (selectedPhotos.length < 3) {
      setSelectedPhotos([...selectedPhotos, uri]);
    } else {
      Alert.alert("Limit reached", "You can select up to 3 photos.");
    } */
  };
  useEffect(() => {
    // Keyboard event listeners
    const keyboardDidShowListener = Keyboard.addListener('keyboardWillShow', () => {

      handleToggleBottomSheet(null, 0)
      setKeyboardVisible(true); // Keyboard is visible
      console.log('Keyboard is up');
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardWillHide', () => {
      setKeyboardVisible(false); // Keyboard is hidden
      console.log('Keyboard is down');
    });

    const fetchPhotos = async () => {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert("Permission required", "Please allow access to media library.");
        return;
      }

      const media = await MediaLibrary.getAssetsAsync({
        mediaType: 'photo',
        first: 100,
        sortBy: [['creationTime', false]]
      });
      setPhotos(media.assets);
    };

    fetchPhotos();

    // Clean up listeners on unmount
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();

    };
  }, []);



  return (

    <View
      style={[styles.inputContainer, {
        paddingBottom: keyboardVisible ? 10 : 44
      }]}
    >
      <View style={[styles.inputWrapper,{
        height: !selectedPhoto ? 90 : 180
      }]}>
        {
          !selectedPhoto ?
            <TextInput
              style={styles.textInput}
              placeholder={i18n.t('home.chatboxPlaceholder')}
              placeholderTextColor="#999"
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
            />
            :
            <View style={{width: '100%', alignItems: 'flex-start'}}>
              <View style={{position: 'relative'}}>
                <TouchableOpacity 
                  style={{position: 'absolute', top:-5, right: -5, height: 25, width: 25, backgroundColor: '#000', zIndex:2, borderRadius: 50, alignItems: 'center', justifyContent: 'center'}}
                  onPress={()=>{
                    setSelectedPhoto(null)
                  }}
                >
                  <Ionicons name='trash-outline' color={'#fff'} size={14}/>
                </TouchableOpacity>
                <Image
                  source={{ uri: selectedPhoto }}
                  style={{
                    width: (Dimensions.get('window').width - 55) / 4,
                    height: 110,
                    borderRadius: 5,
                  }}
                />
              </View>
            </View>
        }
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%', }}>
          {/* Media Selector */}
          <TouchableOpacity
            style={[styles.sendButtonInactive, { height: 30, paddingHorizontal: 10, alignItems: 'center', justifyContent: 'center', borderRadius: 30, padding: 2 }]}
            onPress={() => {
              handleToggleBottomSheet(
                <RenderBottomSheet
                  key={`sheet-${Date.now()}`}
                  photos={photos}
                  toggleSelectPhoto={toggleSelectPhoto}
                />, 500)
              Keyboard.dismiss()
            }}
          >
            <Text style={{ color: "#999", fontWeight: 500 }}>{i18n.t('home.selectPictures')}</Text>
          </TouchableOpacity>

          {/* Send Button */}
          <TouchableOpacity
            style={[inputText.trim() || selectedPhoto ? styles.sendButtonActive : styles.sendButtonInactive, { height: 30, width: 30, alignItems: 'center', justifyContent: 'center', borderRadius: 30, padding: 2 }]}
            onPress={() => { handleSendMessage(inputText); setInputText(''); setSelectedPhoto(null) }}
            disabled={!inputText.trim() && !selectedPhoto}
          >
            <Ionicons name="arrow-up" size={20} color={inputText.trim() ? "#fff" : "#999"} />
          </TouchableOpacity>
        </View>
      </View>

    </View >
  )
}

export default ChatInputArea

const styles = StyleSheet.create({
  inputContainer: {

    paddingHorizontal: 16,
    width: '100%'
  },
  inputWrapper: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: 'space-between',
    backgroundColor: "#00000010",
    borderWidth: 1.7,
    borderColor: "#00000020",
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 12,

    width: '100%',
    

  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
    width: '100%',

  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 8,
  },
  sendButtonActive: {

    backgroundColor: '#337836'
  },
  sendButtonInactive: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: '#00000030',
    borderRadius: 40
  },
  quickActions: {
    flexDirection: "row",
    gap: 8,
  },
  quickAction: {
    backgroundColor: "#f8f9fa",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#e9ecef",
    width: "47%"
  },
  quickActionText: {
    fontSize: 12,
    color: "#666",
  },
  bottomNav: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    gap: 4,
  },
  navText: {
    fontSize: 12,
    color: "#999",
  },
  activeNavText: {
    color: "#337836",
    fontWeight: "500",
  },
})