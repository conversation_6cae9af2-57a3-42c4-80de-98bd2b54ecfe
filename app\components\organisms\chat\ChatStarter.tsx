import { StyleSheet, Text, View, Animated, Easing, TouchableOpacity } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { AuthContext } from '@/context/AuthContext'
import ButtonGlobal from '@/components/atoms/buttons/ButtonGlobal'
import FoodLoader from '@/components/atoms/loaders/Wrench'
import { Ionicons } from '@expo/vector-icons'
import BottomSheet from '@/components/templates/BottomSheet'
import { useBottomSheet } from '@/context/BottomSheetContext'
import i18n from '@/i18n/i18n'



const ChatStarter = () => {
    const { userData } = useContext(AuthContext)
    const { handleToggleBottomSheet } = useBottomSheet()


    // Animation refs
    const headerOpacity = useRef(new Animated.Value(0)).current
    const headerTranslateY = useRef(new Animated.Value(20)).current
    const fridgeOpacity = useRef(new Animated.Value(0)).current
    const fridgeTranslateY = useRef(new Animated.Value(20)).current
    const button1Opacity = useRef(new Animated.Value(0)).current
    const button1TranslateY = useRef(new Animated.Value(20)).current
    const button2Opacity = useRef(new Animated.Value(0)).current
    const button2TranslateY = useRef(new Animated.Value(20)).current
    const button3Opacity = useRef(new Animated.Value(0)).current
    const button3TranslateY = useRef(new Animated.Value(20)).current

    //animations
    useEffect(() => {
        // Animation sequence
        Animated.sequence([
            // Header animation
            Animated.parallel([
                Animated.timing(headerOpacity, {
                    toValue: 1,
                    duration: 500,
                    easing: Easing.out(Easing.ease),
                    useNativeDriver: true,
                }),
                Animated.timing(headerTranslateY, {
                    toValue: 170,
                    duration: 500,
                    easing: Easing.out(Easing.ease),
                    useNativeDriver: true,
                }),
            ]),
            // Fridge container animation
            Animated.parallel([
                Animated.timing(fridgeOpacity, {
                    toValue: 1,
                    duration: 500,
                    easing: Easing.out(Easing.ease),
                    useNativeDriver: true,
                }),
                Animated.timing(fridgeTranslateY, {
                    toValue: 0,
                    duration: 500,
                    easing: Easing.out(Easing.ease),
                    useNativeDriver: true,
                }),
            ]),
            // Buttons animation (sequential)
            Animated.stagger(400, [
                Animated.parallel([
                    Animated.timing(button1Opacity, {
                        toValue: 1,
                        duration: 800,
                        easing: Easing.out(Easing.ease),
                        useNativeDriver: true,
                    }),
                    Animated.timing(button1TranslateY, {
                        toValue: 0,
                        duration: 800,
                        easing: Easing.out(Easing.ease),
                        useNativeDriver: true,
                    }),
                ]),
                Animated.parallel([
                    Animated.timing(button2Opacity, {
                        toValue: 1,
                        duration: 800,
                        easing: Easing.out(Easing.ease),
                        useNativeDriver: true,
                    }),
                    Animated.timing(button2TranslateY, {
                        toValue: 0,
                        duration: 800,
                        easing: Easing.out(Easing.ease),
                        useNativeDriver: true,
                    }),
                ]),
                Animated.parallel([
                    Animated.timing(button3Opacity, {
                        toValue: 1,
                        duration: 500,
                        easing: Easing.out(Easing.ease),
                        useNativeDriver: true,
                    }),
                    Animated.timing(button3TranslateY, {
                        toValue: 0,
                        duration: 500,
                        easing: Easing.out(Easing.ease),
                        useNativeDriver: true,
                    }),
                ]),
            ]),
        ]).start()
    }, [])


    const rederItemsSheet = () => {
        return (
            <View>
                <Text>OK</Text>
            </View>
        )
    }


    return (
        <View style={styles.container}>
            {/* Texts */}
            <Animated.View
                style={{
                    opacity: headerOpacity,
                    transform: [{ translateY: headerTranslateY }],
                }}
            >
                <Text
                    style={[
                        styles.header,
                        {
                            fontWeight: 700,
                        },
                    ]}
                >
                    {i18n.t('home.welcome')} {userData?.user.name}
                </Text>
                <Text
                    style={[
                        styles.header,
                        {
                            fontSize: 20,
                            marginTop: 10,
                            color: "#00000090",
                        },
                    ]}
                >
                    {i18n.t('home.hero')}
                </Text>
            </Animated.View>

            {/* fridgeCheckContainer */}
       

            {/* Buttons */}
            {/* <View style={{ gap: 10, marginTop: 4

             }}>
                <Animated.View
                    style={{
                        opacity: button1Opacity,
                        transform: [{ translateY: button1TranslateY }],
                    }}
                >
                    <ButtonGlobal
                        text={i18n.t('home.suggest')}
                        style={{ backgroundColor: "#337836" }}
                        textStyle={{ fontWeight: 600 }}
                        gap={10}
                        icon={<Ionicons name='restaurant-outline' size={18} color={'white'} />}
                        onPress={() => { }}
                    />
                </Animated.View>

                <Animated.View
                    style={{
                        opacity: button2Opacity,
                        transform: [{ translateY: button2TranslateY }],
                    }}
                >
                    <ButtonGlobal
                        text={i18n.t('home.groceries')}
                        style={{ backgroundColor: "#00000010", borderWidth: 1, borderColor: '#00000030' }}
                        textStyle={{ color: '#00000080', fontWeight: 600 }}
                        gap={10}
                        icon={<Ionicons name='cart-outline' size={18} color={'#00000080'} />}
                    />
                </Animated.View>

               
            </View> */}
        </View>
    )
}

export default ChatStarter

const styles = StyleSheet.create({
    container: {
        paddingTop: 100,
        paddingHorizontal: 20,
    },
    header: {
        width: '100%',
        textAlign: 'center',
        fontSize: 40,
    },
    fridgeCheckContainer: {
        marginTop: 40,
        marginBottom: 40,
        width: '100%',
        backgroundColor: "rgba(34,197,94,0.1)",
        borderRadius: 20,
        flexDirection: 'row',
        paddingVertical: 20,
        justifyContent: 'center',
        gap: 30,
    },
    fridgeCheckText: {
        fontSize: 14,
    },
    fridgeCheckCount: {
        fontSize: 30,
        fontWeight: 700,
    },
})