import React, { createContext, useState, useContext, ReactNode } from 'react';
import BottomSheet from '@/components/templates/BottomSheet';
import { Keyboard } from 'react-native';

// Define the shape of the context
interface BottomSheetContextProps {
  handleToggleBottomSheet: (content?: ReactNode, sheetHeight?: number) => void;
}

export const BottomSheetContext = createContext<BottomSheetContextProps>({
  handleToggleBottomSheet: () => { }
});

export const useBottomSheet = () => {
  return useContext(BottomSheetContext) as BottomSheetContextProps;
};

export const BottomSheetProvider = ({ children }: { children: ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [sheetContent, setSheetContent] = useState<ReactNode | null>(null);
  const [sheetHeight, setSheetHeight] = useState(400)

  const handleToggleBottomSheet = (content?: ReactNode, sheetHeight?: number) => {


    //remove this if buggy
    if (!content){
      setIsOpen(false)
      setSheetContent(null)
      return
    }

    setSheetContent(content);

    if (sheetHeight) {
      setSheetHeight(sheetHeight)
    }

    //Keyboard.dismiss();

    setIsOpen((prev) => !prev); // Toggle the bottom sheet
  };

  // Reset content when the sheet is closed
  const handleClose = () => {
    setIsOpen(false);
    setSheetContent(null); // Clear content on close
  };

  return (
    <BottomSheetContext.Provider value={{ handleToggleBottomSheet }}>
      {children}
      
      <BottomSheet isOpen={isOpen} onClose={handleClose} height={sheetHeight}>
        {
          
          sheetContent
        }
      </BottomSheet>
      
    </BottomSheetContext.Provider>
  );
};