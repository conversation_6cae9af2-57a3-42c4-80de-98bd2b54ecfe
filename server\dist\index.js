"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const dotenv_1 = tslib_1.__importDefault(require("dotenv"));
dotenv_1.default.config();
const cors_1 = tslib_1.__importDefault(require("cors"));
//import chalk from 'chalk';
const express_1 = tslib_1.__importDefault(require("express"));
const middleware_1 = require("./middleware");
const routes_1 = require("./routes");
const connection_1 = tslib_1.__importDefault(require("./database/connection"));
// Create express app
const app = (0, express_1.default)();
// Middlewares
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(middleware_1.errorHandler);
// Routes
app.use('/notifications', routes_1.notifications);
app.use('/oauth', routes_1.oauth);
const startServer = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    if (process.env.NODE_ENV === 'development') {
        //console.log(chalk.green(`[SERVER] Starting`));
    }
    const PORT = process.env.PORT || 4000;
    try {
        // Await database connection
        yield (0, connection_1.default)();
        if (process.env.NODE_ENV === 'development') {
            //console.log(chalk.green(`[SERVER] Database Connected`));
        }
        // Start the server after successful database connection
        const server = app.listen(PORT, () => {
            if (process.env.NODE_ENV === 'development') {
                //console.log(chalk.green(`[SERVER] Running on`), chalk.yellow.underline(`http://localhost:${PORT}`));
            }
        });
        /* Handle unhandled promise rejections */
        process.on('unhandledRejection', (err) => {
            if (err instanceof Error) {
                if (process.env.NODE_ENV === 'development') {
                    //console.log(chalk.red(`[ERROR] Unhandled Rejection: ${err.message}`));
                }
            }
            // Close server & exit process
            server.close(() => process.exit(1));
        });
    }
    catch (error) {
        if (process.env.NODE_ENV === 'development') {
            //console.log(chalk.red(`[DATABASE] Connection failed: ${error.message}`));
        }
        process.exit(1);
    }
});
startServer();
