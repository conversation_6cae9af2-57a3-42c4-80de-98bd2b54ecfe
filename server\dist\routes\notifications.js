"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const router = express_1.default.Router();
const middleware_1 = require("../middleware");
const schemas_1 = require("../database/schemas");
const authentication_1 = require("../middleware/authentication");
router.route("/save-token").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const { token } = req.body;
    if (!token) {
        return res
            .status(400)
            .json({ success: false, message: "Token is required" });
    }
    const account = yield schemas_1.Account.findById((_a = req.user) === null || _a === void 0 ? void 0 : _a._id);
    if (!account) {
        return res
            .status(404)
            .json({ success: false, message: "Account not found" });
    }
    account.notifications.expo_push_token = token;
    yield account.save();
    res.status(200).json({
        success: true,
        message: "Notification token retrieved",
    });
})));
exports.default = router;
