"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const middleware_1 = require("../middleware");
const express_1 = tslib_1.__importDefault(require("express"));
const oauth_1 = require("../controllers/oauth");
const utils_1 = require("../utils");
const router = express_1.default.Router();
router.post("/login/apple", (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { identityToken } = req.body;
        if (!identityToken) {
            return res.status(400).json({ success: false, error: "Missing identityToken" });
        }
        const payload = yield (0, oauth_1.verifyAppleIdentityToken)(identityToken);
        //console.log(payload)
        const appleId = payload.sub;
        const emailApple = payload.email;
        let account = yield schemas_1.Account.findOne({ email: emailApple, password: (0, utils_1.hashPassword)(appleId) });
        if (!account) {
            console.log("Creating new account for Apple user:", emailApple);
            account = new schemas_1.Account({
                email: emailApple,
                password: (0, utils_1.hashPassword)(appleId), // no password required for Apple users or use appleId
                user: {
                    name: "",
                    surname: "",
                    username: `apple_${appleId.slice(-6)}`,
                    profile_picture: "",
                    birthdate: "",
                    type: "user",
                },
                booleans: {
                    isVerified: true,
                    isAdmin: false,
                },
                // Other fields will fall back to schema defaults
            });
            yield account.save();
        }
        const token = (0, middleware_1.signTokenOAuth)(emailApple, (0, utils_1.hashPassword)(appleId));
        res.status(200).json({ success: true, token, account });
    }
    catch (error) {
        console.error("Apple login error:", error);
        res.status(401).json({ success: false, error: "Invalid or expired identity token" });
    }
}));
router.post("/login/google", (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id, email, name, surname, photo } = req.body;
        let account = yield schemas_1.Account.findOne({ email: email, password: (0, utils_1.hashPassword)(id) });
        if (!account) {
            console.log("Creating new account for Google user:", email);
            account = new schemas_1.Account({
                email: email,
                password: (0, utils_1.hashPassword)(id), // no password required for Apple users or use appleId
                user: {
                    name: name !== null && name !== void 0 ? name : "",
                    surname: surname !== null && surname !== void 0 ? surname : "",
                    username: `${name}-${(0, utils_1.generateRandomNumberString)(4)}`,
                    profile_picture: photo,
                    birthdate: "",
                    type: "user",
                },
                booleans: {
                    isVerified: true,
                    isAdmin: false,
                },
                // Other fields will fall back to schema defaults
            });
            yield account.save();
        }
        const token = (0, middleware_1.signTokenOAuth)(email, (0, utils_1.hashPassword)(id));
        res.status(200).json({ success: true, token, account });
    }
    catch (error) {
        console.error("Google login error:", error);
        res.status(401).json({ success: false, error: "Invalid or expired identity token" });
    }
}));
router.route("/authenticate").get(middleware_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    if (!req.user) {
        return res
            .status(400)
            .json({ success: true, message: "Auth user not found" });
    }
    res
        .status(200)
        .json({ success: true, message: "Authorized Access", data: req.user });
})));
exports.default = router;
