import dotenv from 'dotenv';
dotenv.config();

import cors from 'cors';
import chalk from 'chalk';
import express from 'express';
import { errorHandler } from './middleware';

import { notifications, oauth } from './routes';
import connectDatabase from './database/connection';



// Create express app
const app = express();

// Middlewares
app.use(cors());
app.use(express.json());
app.use(errorHandler);

// Routes
app.use('/notifications', notifications);
app.use('/oauth', oauth);






const startServer = async () => {
  if (process.env.NODE_ENV === 'development') {
    console.log(chalk.green(`[SERVER] Starting`));
  }
  const PORT = process.env.PORT || 4000;

  try {
    // Await database connection
    await connectDatabase();

    if (process.env.NODE_ENV === 'development') {
      console.log(chalk.green(`[SERVER] Database Connected`));
    }


    // Start the server after successful database connection
    const server = app.listen(PORT, () => {
      if (process.env.NODE_ENV === 'development') {
        console.log(chalk.green(`[SERVER] Running on`), chalk.yellow.underline(`http://localhost:${PORT}`));
      }
    });

    /* Handle unhandled promise rejections */
    process.on('unhandledRejection', (err) => {
      if (err instanceof Error) {
        if (process.env.NODE_ENV === 'development') {
          console.log(chalk.red(`[ERROR] Unhandled Rejection: ${err.message}`));
        }
      }
      // Close server & exit process
      server.close(() => process.exit(1));
    });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      console.log(chalk.red(`[DATABASE] Connection failed: ${error.message}`));
    }
    process.exit(1);
  }
};


startServer();